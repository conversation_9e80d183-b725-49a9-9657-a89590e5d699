from typing import Dict, Any, TypedDict, Annotated, List, Optional
import re
import sqlite3
import os
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from app.models.schemas import ConversationState

# Define the subgraph state
class PackageSubgraphState(TypedDict):
    """State for the package subgraph"""
    messages: Annotated[List[BaseMessage], add_messages]
    user_input: str
    extracted_slots: Dict[str, Any]
    query_results: List[Dict[str, Any]]
    response: str
    completed: bool
    slots_count: int

# Database path
DB_PATH = "data/package_data.db"

async def flow_package_start(state: ConversationState, message: str) -> Dict[str, Any]:
    """
    Main package flow entry point - creates and runs the subgraph
    """
    # Create the package subgraph
    subgraph = create_package_subgraph()

    # Initialize subgraph state
    initial_state: PackageSubgraphState = {
        "messages": [HumanMessage(content=message)],
        "user_input": message,
        "extracted_slots": {},
        "query_results": [],
        "response": "",
        "completed": False,
        "slots_count": 0
    }

    # Run the subgraph
    result = await subgraph.ainvoke(initial_state)

    # Update conversation state
    state.current_flow = "package"
    state.conversation_history.append({
        "user": message,
        "bot": result["response"],
        "intent": "package",
        "subgraph": "package_tracking"
    })

    return {
        "response": result["response"],
        "next_action": "completed" if result["completed"] else "continue",
        "state": state,
        "completed": result["completed"]
    }

def create_package_subgraph() -> StateGraph:
    """Create the package tracking subgraph"""

    # Define the subgraph
    workflow = StateGraph(PackageSubgraphState)

    # Add nodes
    workflow.add_node("await_input", await_input_node)
    workflow.add_node("extract_slots", extract_slots_node)
    workflow.add_node("build_query", build_query_node)
    workflow.add_node("final_answer", final_answer_node)

    # Set entry point
    workflow.set_entry_point("await_input")

    # Add edges - simplified flow without loops
    workflow.add_edge("await_input", "extract_slots")
    workflow.add_edge("extract_slots", "build_query")
    workflow.add_edge("build_query", "final_answer")
    workflow.add_edge("final_answer", END)

    return workflow.compile()

# Subgraph Node Functions

async def await_input_node(state: PackageSubgraphState) -> PackageSubgraphState:
    """
    Human Node: Prompt the user for package details
    """
    # Check if this is the first interaction or a follow-up
    if not state["extracted_slots"]:
        # First interaction
        response = "Please provide details about the package you want to check (e.g., package ID, destination, or category)."
    else:
        # Follow-up interaction - ask for missing information
        missing_info = []
        if "package_id" not in state["extracted_slots"]:
            missing_info.append("package ID")
        if "destination" not in state["extracted_slots"] and "category" not in state["extracted_slots"]:
            missing_info.append("category or destination")

        if missing_info:
            response = f"I need more information. Could you please provide: {', '.join(missing_info)}?"
        else:
            response = "Let me search for your package information."

    # Add AI message to conversation
    state["messages"].append(AIMessage(content=response))
    state["response"] = response

    return state

async def extract_slots_node(state: PackageSubgraphState) -> PackageSubgraphState:
    """
    Function Node: Extract relevant fields from user input
    """
    user_input = state["user_input"]
    extracted_slots = state["extracted_slots"].copy()

    # Extract package_id (format: PK-XXX-XXX-XXX-XXX or similar)
    package_id_pattern = r'\b(PK-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+)\b'
    package_id_match = re.search(package_id_pattern, user_input.upper())
    if package_id_match:
        extracted_slots["package_id"] = package_id_match.group(1)

    # Extract category keywords
    categories = {
        "aesthetic": ["aesthetic", "beauty", "cosmetic", "botox", "surgery", "lift", "augmentation"],
        "vaccine": ["vaccine", "vaccination", "immunization", "shot"],
        "checkup": ["checkup", "health check", "examination", "screening", "test"],
        "dental": ["dental", "teeth", "tooth", "oral"],
        "wellness": ["wellness", "spa", "massage", "therapy"]
    }

    user_lower = user_input.lower()
    for category, keywords in categories.items():
        if any(keyword in user_lower for keyword in keywords):
            extracted_slots["category"] = category
            break

    # Extract price range
    price_pattern = r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:baht|thb|฿|\$)'
    price_matches = re.findall(price_pattern, user_input.lower())
    if price_matches:
        prices = [float(p.replace(',', '')) for p in price_matches]
        extracted_slots["price_min"] = min(prices)
        extracted_slots["price_max"] = max(prices)

    # Extract destination/location
    locations = ["bangkok", "phuket", "samui", "chiang mai", "pattaya", "hua hin"]
    for location in locations:
        if location in user_lower:
            extracted_slots["destination"] = location.title()
            break

    # Update state
    state["extracted_slots"] = extracted_slots
    state["slots_count"] = len(extracted_slots)

    return state

# Removed should_continue_extraction function as we simplified the flow

async def build_query_node(state: PackageSubgraphState) -> PackageSubgraphState:
    """
    Function Node: Build and execute SQL query
    """
    extracted_slots = state["extracted_slots"]

    # Build SQL query based on extracted slots
    query_parts = []
    params = []

    base_query = "SELECT package_id, name_en, category, price_min, price_max, description_short FROM package_data WHERE "

    if "package_id" in extracted_slots:
        query_parts.append("package_id = ?")
        params.append(extracted_slots["package_id"])

    if "category" in extracted_slots:
        # Map category to database categories
        category_mapping = {
            "aesthetic": "ศัลยกรรมตกแต่ง",
            "vaccine": "วัคซีน",
            "checkup": "ตรวจสุขภาพ",
            "dental": "ทันตกรรม",
            "wellness": "สุขภาพ"
        }
        db_category = category_mapping.get(extracted_slots["category"])
        if db_category:
            query_parts.append("category LIKE ?")
            params.append(f"%{db_category}%")

    if "price_min" in extracted_slots and "price_max" in extracted_slots:
        query_parts.append("(price_min BETWEEN ? AND ? OR price_max BETWEEN ? AND ?)")
        params.extend([
            extracted_slots["price_min"], extracted_slots["price_max"],
            extracted_slots["price_min"], extracted_slots["price_max"]
        ])

    if "destination" in extracted_slots:
        # For medical packages, destination might be in available_for field
        query_parts.append("available_for LIKE ?")
        params.append(f"%{extracted_slots['destination']}%")

    # If no specific criteria, do a general search
    if not query_parts:
        query_parts.append("1=1")  # Return all results

    # Use OR for broader search, AND for more specific search
    if len(query_parts) == 1:
        final_query = base_query + query_parts[0] + " LIMIT 10"
    else:
        final_query = base_query + " OR ".join(query_parts) + " LIMIT 10"

    # Execute query
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        cursor = conn.cursor()
        cursor.execute(final_query, params)
        results = [dict(row) for row in cursor.fetchall()]
        conn.close()

        state["query_results"] = results
    except Exception as e:
        print(f"Database error: {e}")
        state["query_results"] = []

    return state

async def final_answer_node(state: PackageSubgraphState) -> PackageSubgraphState:
    """
    Function Node: Generate natural language response from query results
    """
    query_results = state["query_results"]

    # Check if we have enough information
    if state["slots_count"] == 0:
        response = "Please provide details about the package you want to check (e.g., package ID, destination, or category like 'botox', 'vaccine', 'checkup')."
        state["completed"] = False  # Need more information
    elif not query_results:
        # No results found
        response = "Sorry, we couldn't find any package matching that information. Could you double-check the details or try different search criteria?"
    elif len(query_results) == 1:
        # Single result - provide detailed information
        package = query_results[0]
        response = f"""📦 **{package['name_en']}**

🆔 Package ID: {package['package_id']}
🏷️ Category: {package['category']}
💰 Price: {package['price_min']:,.0f}"""

        if package['price_max'] != package['price_min']:
            response += f" - {package['price_max']:,.0f}"
        response += " THB"

        if package['description_short']:
            response += f"\n📝 Description: {package['description_short']}"

        response += "\n\nWould you like more information about this package?"

    else:
        # Multiple results - provide summary list
        response = f"I found {len(query_results)} packages matching your criteria:\n\n"

        for i, package in enumerate(query_results[:5], 1):  # Show max 5 results
            price_str = f"{package['price_min']:,.0f}"
            if package['price_max'] != package['price_min']:
                price_str += f" - {package['price_max']:,.0f}"
            price_str += " THB"

            response += f"{i}. **{package['name_en']}** ({package['package_id']})\n"
            response += f"   💰 {price_str}\n\n"

        if len(query_results) > 5:
            response += f"... and {len(query_results) - 5} more packages.\n\n"

        response += "Please provide a specific package ID for detailed information."

    # Add AI message and mark as completed
    state["messages"].append(AIMessage(content=response))
    state["response"] = response
    state["completed"] = True

    return state
