# 🤖 Receptionist Chatbot - FastAPI + LangGraph + LangGraph Studio

A modular conversational chatbot prototype built with Python FastAPI and LangGraph, featuring intent classification, modular conversation flows, and LangGraph Studio integration for visualization and debugging.

## ✨ Features

### 🎯 Intent Classification
- Detects user intent from natural language input
- Uses OpenAI GPT-3.5-turbo with rule-based fallback
- Supports 5 core intents: `booking`, `greeting`, `info`, `package`, `unknown`

### 🔄 Modular Conversation Flows
Each intent routes to a distinct subgraph handler:

| Intent | Flow Node | Description |
|--------|-----------|-------------|
| `booking` | `flow_booking_start` | Gathers service, date, time, and confirms booking |
| `greeting` | `flow_greeting_start` | Returns standard greeting responses |
| `info` | `flow_info_start` | Looks up FAQs and returns answers |
| `package` | `flow_package_start` | **Nested subgraph**: await_input → extract_slots → build_query |
| `unknown` | `flow_unknown_start` | Fallback handler for out-of-scope input |

### 📊 LangGraph Studio Integration
- Live graph visualization and debugging
- Session history tracking
- Flow transition monitoring
- Real-time state inspection

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- OpenAI API key (optional, falls back to rule-based classification)

### Installation

1. **Clone and setup**:
```bash
cd Receptionist_Chatbot
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
# Edit .env file
OPENAI_API_KEY=your_openai_api_key_here
ENVIRONMENT=development
LOG_LEVEL=INFO
```

3. **Run the application**:
```bash
uvicorn app.main:app --reload
```

4. **Access the chatbot**:
- Web interface: http://localhost:8000
- API docs: http://localhost:8000/docs
- Health check: http://localhost:8000/health

### LangGraph Studio Setup

1. **Install LangGraph Studio**:
```bash
pip install langgraph-studio
```

2. **Launch Studio**:
```bash
langgraph studio
```

3. **Open project**: Point to the `langgraph.json` file in the project root

## 📁 Project Structure

```
Receptionist_Chatbot/
├── app/
│   ├── main.py                 # FastAPI application
│   ├── config.py               # Configuration settings
│   ├── models/
│   │   └── schemas.py          # Pydantic models
│   ├── services/
│   │   ├── intent_classifier.py # Intent classification
│   │   └── conversation_graph.py # LangGraph conversation flow
│   └── flows/
│       ├── booking_flow.py     # Booking intent handler
│       ├── greeting_flow.py    # Greeting intent handler
│       ├── info_flow.py        # Info intent handler
│       ├── package_flow.py     # Package intent (nested subgraph)
│       └── unknown_flow.py     # Unknown intent fallback
├── requirements.txt
├── .env
├── langgraph.json             # LangGraph Studio config
└── README.md
```

## 🎮 Usage Examples

### Booking Flow
```
User: "I'd like to book an appointment"
Bot: "What type of service would you like to book?"
User: "Doctor consultation tomorrow at 2pm"
Bot: "Let me confirm your booking: Service: consultation, Date: tomorrow, Time: 2pm"
```

### Package Flow (Nested Subgraph)
```
User: "I want to send a package"
Bot: "I'll help you send a package. Let me gather some information."
User: "5kg package to New York for John Smith"
Bot: "Here's your shipping summary: 📍 Destination: New York, ⚖️ Weight: 5kg, 👤 Recipient: John Smith"
```

### Info Flow
```
User: "What are your hours?"
Bot: "Our office hours are Monday-Friday 8:00 AM to 6:00 PM, and Saturday 9:00 AM to 2:00 PM."
```

## 🔧 API Endpoints

### POST `/chat`
Process a chat message
```json
{
  "message": "Hello, I need help",
  "session_id": "optional-session-id"
}
```

Response:
```json
{
  "response": "Hello! Welcome to our receptionist service...",
  "intent": "greeting",
  "confidence": 0.95,
  "session_id": "generated-session-id",
  "metadata": {
    "flow": "greeting",
    "collected_data": {}
  }
}
```

### GET `/intents`
Get supported intents
```json
{
  "intents": ["booking", "greeting", "info", "package", "unknown"]
}
```

## 🏗️ Architecture

### Intent Classification
- **Primary**: OpenAI GPT-3.5-turbo for accurate classification
- **Fallback**: Rule-based pattern matching for reliability
- **Confidence scoring**: Ensures quality responses

### Conversation State Management
- Session-based state persistence
- Conversation history tracking
- Collected data storage for multi-turn conversations

### Modular Flow Design
- Each intent has dedicated flow handler
- Nested subgraphs for complex workflows (package flow)
- Stateful conversation management

## 🔍 LangGraph Studio Features

1. **Graph Visualization**: See the conversation flow in real-time
2. **State Inspection**: Monitor conversation state changes
3. **Debugging**: Step through conversation logic
4. **Session History**: Review past conversations
5. **Performance Metrics**: Track response times and success rates

## 🛠️ Development

### Adding New Intents

1. **Update configuration**:
```python
# app/config.py
SUPPORTED_INTENTS = [..., "new_intent"]
```

2. **Create flow handler**:
```python
# app/flows/new_intent_flow.py
async def flow_new_intent_start(state: ConversationState, message: str):
    # Implementation
    pass
```

3. **Update conversation graph**:
```python
# app/services/conversation_graph.py
workflow.add_node("flow_new_intent_start", self._new_intent_flow_node)
```

### Testing
```bash
# Run tests (when implemented)
pytest

# Test specific flow
python -m pytest tests/test_booking_flow.py
```

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For questions or issues, please open a GitHub issue or contact the development team.
