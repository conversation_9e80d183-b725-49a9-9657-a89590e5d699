"""
Comprehensive test suite for FastAPI and LangGraph Studio compatibility
"""

import asyncio
from fastapi.testclient import Test<PERSON>lient
from app.main import app
from app.services.conversation_graph import process_message, create_conversation_graph
from app.flows.package_flow import create_package_subgraph
from app.models.schemas import ConversationState
from app.config import settings
from datetime import datetime

# FastAPI Test Client
client = TestClient(app)

async def test_fastapi_compatibility():
    """Test FastAPI endpoints and functionality"""
    print("🧪 Testing FastAPI Compatibility...")

    # Test health endpoint
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"
    print("   ✅ Health endpoint working")

    # Test intents endpoint
    response = client.get("/intents")
    assert response.status_code == 200
    assert "intents" in response.json()
    print("   ✅ Intents endpoint working")

    # Test root endpoint
    response = client.get("/")
    assert response.status_code == 200
    assert "Receptionist Chatbot" in response.text
    print("   ✅ Root endpoint serving chat interface")

    # Test chat endpoint
    test_message = {
        "message": "Hello, how are you?",
        "session_id": "test_session_fastapi"
    }
    response = client.post("/chat", json=test_message)
    assert response.status_code == 200

    data = response.json()
    assert "response" in data
    assert "intent" in data
    assert "confidence" in data
    assert "session_id" in data
    print("   ✅ Chat endpoint working correctly")
    print(f"   ✅ Response: {data['response'][:50]}...")
    print(f"   ✅ Intent: {data['intent']} (confidence: {data['confidence']})")

async def test_langgraph_studio_compatibility():
    """Test LangGraph Studio graph creation and execution"""
    print("\n🧪 Testing LangGraph Studio Compatibility...")

    # Test main graph creation
    try:
        graph = create_conversation_graph()
        assert graph is not None
        print("   ✅ Main conversation graph created successfully")
        print(f"   ✅ Graph nodes: {list(graph.nodes.keys())}")
    except Exception as e:
        print(f"   ❌ Error creating main graph: {e}")
        raise

    # Test package subgraph creation
    try:
        subgraph = create_package_subgraph()
        assert subgraph is not None
        print("   ✅ Package subgraph created successfully")
        print(f"   ✅ Subgraph nodes: {list(subgraph.nodes.keys())}")
    except Exception as e:
        print(f"   ❌ Error creating package subgraph: {e}")
        raise

    # Test graph execution
    try:
        result = await process_message("Hello there!", "test_studio_session")
        assert "response" in result
        assert "intent" in result
        print("   ✅ Graph execution successful")
        print(f"   ✅ Response: {result['response'][:50]}...")
    except Exception as e:
        print(f"   ❌ Error executing graph: {e}")
        raise

async def test_package_subgraph_functionality():
    """Test package subgraph with various scenarios"""
    print("\n🧪 Testing Package Subgraph Functionality...")

    test_cases = [
        {
            "input": "I'm looking for a botox package",
            "expected_intent": "package",
            "description": "Aesthetic package search"
        },
        {
            "input": "Show me vaccine packages",
            "expected_intent": "package",
            "description": "Vaccine package search"
        },
        {
            "input": "I need package PK-VAC-HFMD-EV71-001",
            "expected_intent": "package",
            "description": "Specific package ID search"
        }
    ]

    for i, test_case in enumerate(test_cases, 1):
        try:
            result = await process_message(test_case["input"], f"test_package_{i}")

            assert result["intent"] == test_case["expected_intent"]
            assert len(result["response"]) > 0

            print(f"   ✅ Test {i} ({test_case['description']}): PASSED")
            print(f"      Response: {result['response'][:100]}...")

        except Exception as e:
            print(f"   ❌ Test {i} ({test_case['description']}): FAILED - {e}")
            raise

async def test_error_handling():
    """Test error handling and recovery"""
    print("\n🧪 Testing Error Handling...")

    # Test with invalid input
    try:
        result = await process_message("", "test_error_session")
        assert "response" in result
        print("   ✅ Empty message handled gracefully")
    except Exception as e:
        print(f"   ❌ Error handling empty message: {e}")

    # Test with very long input
    try:
        long_message = "test " * 1000  # Very long message
        result = await process_message(long_message, "test_long_session")
        assert "response" in result
        print("   ✅ Long message handled gracefully")
    except Exception as e:
        print(f"   ❌ Error handling long message: {e}")

async def test_configuration():
    """Test configuration settings"""
    print("\n🧪 Testing Configuration...")

    # Test settings are loaded
    assert settings.SUPPORTED_INTENTS is not None
    assert len(settings.SUPPORTED_INTENTS) > 0
    print("   ✅ Supported intents configured")

    # Test database path
    assert settings.DATABASE_PATH is not None
    print(f"   ✅ Database path configured: {settings.DATABASE_PATH}")

    # Test environment detection
    print(f"   ✅ Environment: {settings.ENVIRONMENT}")
    print(f"   ✅ Debug mode: {settings.DEBUG}")
    print(f"   ✅ Log level: {settings.LOG_LEVEL}")

async def run_all_tests():
    """Run all compatibility tests"""
    print("🚀 Starting Comprehensive Compatibility Tests")
    print("=" * 60)

    try:
        await test_fastapi_compatibility()
        await test_langgraph_studio_compatibility()
        await test_package_subgraph_functionality()
        await test_error_handling()
        await test_configuration()

        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! Codebase is compatible with both FastAPI and LangGraph Studio")

    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(run_all_tests())
