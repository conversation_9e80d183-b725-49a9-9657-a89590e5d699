from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import logging
from contextlib import asynccontextmanager

from app.models.schemas import ChatMessage, ChatResponse
from app.services.conversation_graph import process_message
from app.config import settings

# Configure logging
logging.basicConfig(level=getattr(logging, settings.LOG_LEVEL))
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    logger.info("Starting Receptionist Chatbot API")
    yield
    logger.info("Shutting down Receptionist Chatbot API")

# Create FastAPI app
app = FastAPI(
    title="Receptionist Chatbot API",
    description="A modular conversational chatbot using FastAPI and LangGraph",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve a simple chat interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Receptionist Chatbot</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .chat-container { border: 1px solid #ddd; height: 400px; overflow-y: auto; padding: 10px; margin-bottom: 10px; }
            .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
            .user { background-color: #e3f2fd; text-align: right; }
            .bot { background-color: #f5f5f5; }
            .input-container { display: flex; gap: 10px; }
            input[type="text"] { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            button { padding: 10px 20px; background-color: #2196f3; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background-color: #1976d2; }
            .metadata { font-size: 0.8em; color: #666; margin-top: 5px; }
        </style>
    </head>
    <body>
        <h1>🤖 Receptionist Chatbot</h1>
        <p>I can help you with booking appointments, getting information, and package services!</p>
        
        <div id="chat-container" class="chat-container"></div>
        
        <div class="input-container">
            <input type="text" id="message-input" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">Send</button>
        </div>
        
        <script>
            let sessionId = null;
            
            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }
            
            async function sendMessage() {
                const input = document.getElementById('message-input');
                const message = input.value.trim();
                if (!message) return;
                
                // Add user message to chat
                addMessage(message, 'user');
                input.value = '';
                
                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            session_id: sessionId
                        })
                    });
                    
                    const data = await response.json();
                    sessionId = data.session_id;
                    
                    // Add bot response to chat
                    addMessage(data.response, 'bot', data);
                    
                } catch (error) {
                    addMessage('Sorry, I encountered an error. Please try again.', 'bot');
                    console.error('Error:', error);
                }
            }
            
            function addMessage(text, sender, metadata = null) {
                const container = document.getElementById('chat-container');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                let content = `<strong>${sender === 'user' ? 'You' : 'Bot'}:</strong> ${text}`;
                
                if (metadata && sender === 'bot') {
                    content += `<div class="metadata">Intent: ${metadata.intent} (${(metadata.confidence * 100).toFixed(1)}%)</div>`;
                }
                
                messageDiv.innerHTML = content;
                container.appendChild(messageDiv);
                container.scrollTop = container.scrollHeight;
            }
            
            // Add welcome message
            addMessage("Hello! I'm your virtual receptionist. How can I help you today?", 'bot');
        </script>
    </body>
    </html>
    """

@app.post("/chat", response_model=ChatResponse)
async def chat(message: ChatMessage):
    """Process a chat message and return bot response"""
    try:
        logger.info(f"Processing message: {message.message[:50]}...")
        
        # Process message through conversation graph
        result = await process_message(
            message=message.message,
            session_id=message.session_id
        )
        
        return ChatResponse(
            response=result["response"],
            intent=result["intent"],
            confidence=result["confidence"],
            session_id=result["session_id"],
            metadata=result.get("metadata")
        )
        
    except Exception as e:
        logger.error(f"Error processing message: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": "1.0.0"}

@app.get("/intents")
async def get_supported_intents():
    """Get list of supported intents"""
    return {"intents": settings.SUPPORTED_INTENTS}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
