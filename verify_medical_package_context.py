"""
Comprehensive verification that all package-related content is about medical packages
"""

import re
import sqlite3
from app.config import settings

def verify_database_content():
    """Verify database contains only medical packages"""
    print("🔍 Verifying Database Content...")
    
    try:
        conn = sqlite3.connect(settings.DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get all packages
        cursor.execute("SELECT package_id, name_en, category, description_short FROM package_data LIMIT 10")
        packages = cursor.fetchall()
        
        print(f"   ✅ Found {len(packages)} sample packages in database")
        
        medical_categories = [
            "ศัลยกรรมตกแต่ง",  # Aesthetic surgery
            "วัคซีนป้องกันโรค",  # Vaccines
            "ตรวจสุขภาพ",       # Health checkups
            "ตรวจสุขภาพเฉพาะทาง"  # Specialized health checks
        ]
        
        for package in packages:
            is_medical = any(cat in package['category'] for cat in medical_categories)
            status = "✅" if is_medical else "❌"
            print(f"   {status} {package['package_id']}: {package['name_en']} ({package['category']})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return False

def verify_code_comments():
    """Verify code comments and strings mention medical packages"""
    print("\n🔍 Verifying Code Comments and Strings...")
    
    files_to_check = [
        "app/flows/package_flow.py",
        "app/services/intent_classifier.py", 
        "app/models/schemas.py",
        "README.md"
    ]
    
    delivery_keywords = [
        "delivery", "shipping", "courier", "send", "mail", 
        "tracking", "parcel", "shipment", "recipient", "sender"
    ]
    
    medical_keywords = [
        "medical", "health", "vaccine", "aesthetic", "wellness",
        "checkup", "treatment", "surgery", "botox", "examination"
    ]
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
                
            # Check for delivery keywords (should be minimal/none)
            delivery_matches = sum(1 for keyword in delivery_keywords if keyword in content)
            
            # Check for medical keywords (should be present)
            medical_matches = sum(1 for keyword in medical_keywords if keyword in content)
            
            delivery_status = "✅" if delivery_matches <= 2 else "❌"  # Allow minimal delivery references
            medical_status = "✅" if medical_matches > 0 else "❌"
            
            print(f"   {file_path}:")
            print(f"     {delivery_status} Delivery keywords: {delivery_matches}")
            print(f"     {medical_status} Medical keywords: {medical_matches}")
            
        except Exception as e:
            print(f"   ❌ Error checking {file_path}: {e}")

def verify_intent_patterns():
    """Verify intent classifier patterns are medical-focused"""
    print("\n🔍 Verifying Intent Classifier Patterns...")
    
    from app.services.intent_classifier import IntentClassifier
    
    classifier = IntentClassifier()
    package_patterns = classifier.intent_patterns.get("package", [])
    
    print(f"   ✅ Found {len(package_patterns)} package patterns")
    
    for i, pattern in enumerate(package_patterns, 1):
        # Check if pattern contains medical terms
        medical_terms = ["medical", "health", "vaccine", "aesthetic", "checkup", "treatment", "therapy", "consultation", "botox", "surgery"]
        has_medical = any(term in pattern for term in medical_terms)
        
        # Check if pattern contains delivery terms
        delivery_terms = ["delivery", "shipping", "courier", "send", "mail", "tracking", "parcel"]
        has_delivery = any(term in pattern for term in delivery_terms)
        
        status = "✅" if has_medical and not has_delivery else "❌"
        print(f"   {status} Pattern {i}: {pattern}")

def verify_schema_fields():
    """Verify schema fields are medical-focused"""
    print("\n🔍 Verifying Schema Fields...")
    
    try:
        from app.models.schemas import MedicalPackageData
        
        # Get field names
        fields = list(MedicalPackageData.model_fields.keys())
        print(f"   ✅ MedicalPackageData fields: {fields}")
        
        # Check for medical-appropriate fields
        medical_fields = ["package_id", "category", "price_range", "location", "target_group"]
        delivery_fields = ["weight", "sender_name", "recipient_name", "destination"]
        
        has_medical = any(field in fields for field in medical_fields)
        has_delivery = any(field in fields for field in delivery_fields)
        
        medical_status = "✅" if has_medical else "❌"
        delivery_status = "✅" if not has_delivery else "❌"
        
        print(f"   {medical_status} Has medical-appropriate fields")
        print(f"   {delivery_status} No delivery-specific fields")
        
    except Exception as e:
        print(f"   ❌ Error checking schema: {e}")

def main():
    """Run all verification checks"""
    print("🚀 Comprehensive Medical Package Context Verification")
    print("=" * 60)
    
    # Run all verification checks
    db_ok = verify_database_content()
    verify_code_comments()
    verify_intent_patterns()
    verify_schema_fields()
    
    print("\n" + "=" * 60)
    if db_ok:
        print("🎉 VERIFICATION COMPLETE: All package content is medical/wellness focused!")
        print("✅ No package delivery/shipping context found")
        print("✅ All packages are medical treatments, vaccines, or health checkups")
    else:
        print("❌ VERIFICATION FAILED: Issues found in package context")

if __name__ == "__main__":
    main()
