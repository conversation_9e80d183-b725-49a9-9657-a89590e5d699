from typing import Dict, Any, TypedDict, Annotated
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
import uuid
from datetime import datetime

from app.models.schemas import ConversationState
from app.services.intent_classifier import IntentClassifier
from app.flows.greeting_flow import flow_greeting_start
from app.flows.booking_flow import flow_booking_start
from app.flows.info_flow import flow_info_start
from app.flows.package_flow import flow_package_start
from app.flows.unknown_flow import flow_unknown_start

class GraphState(TypedDict):
    """State for the conversation graph"""
    messages: Annotated[list[BaseMessage], add_messages]
    session_id: str
    current_intent: str
    confidence: float
    conversation_state: Dict[str, Any]  # Simplified for Studio compatibility
    response: str
    completed: bool

class ConversationGraph:
    def __init__(self):
        self.intent_classifier = IntentClassifier()
        self.graph = self._create_graph()

    def _get_or_create_conversation_state(self, state: GraphState) -> ConversationState:
        """Helper method to get or create conversation state"""
        conv_state_dict = state.get("conversation_state", {})
        if not conv_state_dict:
            # Create default conversation state if not present
            conv_state_dict = {
                "session_id": state.get("session_id", "default"),
                "current_intent": None,
                "current_flow": None,
                "collected_data": {},
                "conversation_history": [],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00"
            }
        return ConversationState(**conv_state_dict)

    def _create_graph(self) -> StateGraph:
        """Create the main conversation graph"""

        # Define the graph
        workflow = StateGraph(GraphState)

        # Add nodes
        workflow.add_node("classify_intent", self._classify_intent_node)
        workflow.add_node("flow_greeting_start", self._greeting_flow_node)
        workflow.add_node("flow_booking_start", self._booking_flow_node)
        workflow.add_node("flow_info_start", self._info_flow_node)
        workflow.add_node("flow_package_start", self._package_flow_node)
        workflow.add_node("flow_unknown_start", self._unknown_flow_node)

        # Set entry point
        workflow.set_entry_point("classify_intent")

        # Add conditional edges from intent classification
        workflow.add_conditional_edges(
            "classify_intent",
            self._route_intent,
            {
                "greeting": "flow_greeting_start",
                "booking": "flow_booking_start",
                "info": "flow_info_start",
                "package": "flow_package_start",
                "unknown": "flow_unknown_start"
            }
        )

        # Add edges to END for all flow nodes
        workflow.add_edge("flow_greeting_start", END)
        workflow.add_edge("flow_booking_start", END)
        workflow.add_edge("flow_info_start", END)
        workflow.add_edge("flow_package_start", END)
        workflow.add_edge("flow_unknown_start", END)

        return workflow.compile()

    async def _classify_intent_node(self, state: GraphState) -> GraphState:
        """Node to classify user intent"""
        # Get the latest message
        latest_message = state["messages"][-1].content if state["messages"] else ""

        # Classify intent
        intent, confidence = await self.intent_classifier.classify_intent(latest_message)

        # Update state
        state["current_intent"] = intent
        state["confidence"] = confidence

        return state

    def _route_intent(self, state: GraphState) -> str:
        """Route to appropriate flow based on classified intent"""
        return state["current_intent"]

    async def _greeting_flow_node(self, state: GraphState) -> GraphState:
        """Handle greeting flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_greeting_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].dict()

        return state

    async def _booking_flow_node(self, state: GraphState) -> GraphState:
        """Handle booking flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_booking_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].dict()

        return state

    async def _info_flow_node(self, state: GraphState) -> GraphState:
        """Handle info flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_info_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].dict()

        return state

    async def _package_flow_node(self, state: GraphState) -> GraphState:
        """Handle package flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_package_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].dict()

        return state

    async def _unknown_flow_node(self, state: GraphState) -> GraphState:
        """Handle unknown flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_unknown_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].dict()

        return state

# Global conversation states storage (in production, use a database)
conversation_states: Dict[str, ConversationState] = {}

async def process_message(message: str, session_id: str = None) -> Dict[str, Any]:
    """Process a user message through the conversation graph"""

    # Generate session ID if not provided
    if not session_id:
        session_id = str(uuid.uuid4())

    # Get or create conversation state
    if session_id not in conversation_states:
        conversation_states[session_id] = ConversationState(
            session_id=session_id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    conv_state = conversation_states[session_id]
    conv_state.updated_at = datetime.now()

    # Create graph instance
    graph = ConversationGraph()

    # Prepare initial state
    from langchain_core.messages import HumanMessage
    initial_state: GraphState = {
        "messages": [HumanMessage(content=message)],
        "session_id": session_id,
        "current_intent": "",
        "confidence": 0.0,
        "conversation_state": conv_state.dict(),  # Convert to dict for Studio compatibility
        "response": "",
        "completed": False
    }

    # Run the graph
    result = await graph.graph.ainvoke(initial_state)

    # Update stored conversation state
    updated_conv_state = ConversationState(**result["conversation_state"])
    conversation_states[session_id] = updated_conv_state

    return {
        "response": result["response"],
        "intent": result["current_intent"],
        "confidence": result["confidence"],
        "session_id": session_id,
        "completed": result["completed"],
        "metadata": {
            "flow": updated_conv_state.current_flow,
            "collected_data": updated_conv_state.collected_data
        }
    }

def create_conversation_graph():
    """Factory function for LangGraph Studio"""
    # Create and return the compiled graph for Studio
    conversation_graph = ConversationGraph()
    return conversation_graph.graph

# For direct Studio compatibility, also export the graph
graph = create_conversation_graph()