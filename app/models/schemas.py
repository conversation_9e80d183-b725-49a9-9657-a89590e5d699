from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

class ChatMessage(BaseModel):
    message: str = Field(..., description="User input message")
    session_id: Optional[str] = Field(None, description="Session identifier for conversation continuity")

class ChatResponse(BaseModel):
    response: str = Field(..., description="Bot response message")
    intent: str = Field(..., description="Detected intent")
    confidence: float = Field(..., description="Intent classification confidence")
    session_id: str = Field(..., description="Session identifier")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional response metadata")

class ConversationState(BaseModel):
    session_id: str
    current_intent: Optional[str] = None
    current_flow: Optional[str] = None
    collected_data: Dict[str, Any] = Field(default_factory=dict)
    conversation_history: List[Dict[str, str]] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

class BookingData(BaseModel):
    service: Optional[str] = None
    date: Optional[str] = None
    time: Optional[str] = None
    confirmed: bool = False

class PackageData(BaseModel):
    package_id: Optional[str] = None
    destination: Optional[str] = None
    weight: Optional[str] = None
    sender_name: Optional[str] = None
    recipient_name: Optional[str] = None
