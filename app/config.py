import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # Intent classification settings
    INTENT_CONFIDENCE_THRESHOLD: float = 0.7
    
    # Supported intents
    SUPPORTED_INTENTS = [
        "booking",
        "greeting", 
        "info",
        "package",
        "unknown"
    ]

settings = Settings()
