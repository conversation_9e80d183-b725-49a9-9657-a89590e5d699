from typing import Dict, Any
import re
from app.models.schemas import ConversationState, PackageData

async def flow_package_start(state: ConversationState, message: str) -> Dict[str, Any]:
    """
    Handle package intent - nested subgraph with await_input → extract_slots → build_query
    """
    # Initialize package data if not exists
    if "package_data" not in state.collected_data:
        state.collected_data["package_data"] = PackageData().dict()
        state.collected_data["package_step"] = "await_input"
    
    current_step = state.collected_data.get("package_step", "await_input")
    
    if current_step == "await_input":
        return await _await_input_step(state, message)
    elif current_step == "extract_slots":
        return await _extract_slots_step(state, message)
    elif current_step == "build_query":
        return await _build_query_step(state, message)
    else:
        # Reset to beginning if unknown step
        state.collected_data["package_step"] = "await_input"
        return await _await_input_step(state, message)

async def _await_input_step(state: ConversationState, message: str) -> Dict[str, Any]:
    """First step: Await user input and determine package operation"""
    message_lower = message.lower()
    
    # Determine if user wants to send or track
    if any(word in message_lower for word in ["send", "ship", "mail", "new package"]):
        operation = "send"
        response = "I'll help you send a package. Let me gather some information."
    elif any(word in message_lower for word in ["track", "tracking", "status", "where is"]):
        operation = "track"
        response = "I'll help you track your package. Please provide your tracking number."
    else:
        operation = "unknown"
        response = "I can help you send a new package or track an existing one. Which would you like to do?"
    
    # Update state
    state.collected_data["package_operation"] = operation
    state.collected_data["package_step"] = "extract_slots"
    state.current_flow = "package"
    
    state.conversation_history.append({
        "user": message,
        "bot": response,
        "intent": "package",
        "step": "await_input"
    })
    
    return {
        "response": response,
        "next_action": "extract_slots",
        "state": state,
        "completed": False
    }

async def _extract_slots_step(state: ConversationState, message: str) -> Dict[str, Any]:
    """Second step: Extract relevant information slots from user input"""
    package_data = PackageData(**state.collected_data["package_data"])
    operation = state.collected_data.get("package_operation", "unknown")
    
    # Extract information based on operation type
    if operation == "send":
        extracted_info = _extract_shipping_info(message)
        
        # Update package data
        if extracted_info.get("destination"):
            package_data.destination = extracted_info["destination"]
        if extracted_info.get("weight"):
            package_data.weight = extracted_info["weight"]
        if extracted_info.get("recipient_name"):
            package_data.recipient_name = extracted_info["recipient_name"]
        if extracted_info.get("sender_name"):
            package_data.sender_name = extracted_info["sender_name"]
            
    elif operation == "track":
        # Extract tracking number
        tracking_match = re.search(r'\b([A-Z0-9]{8,})\b', message.upper())
        if tracking_match:
            package_data.package_id = tracking_match.group(1)
    
    # Update state
    state.collected_data["package_data"] = package_data.dict()
    state.collected_data["package_step"] = "build_query"
    
    # Determine response based on what's still needed
    response = _determine_next_package_question(package_data, operation)
    
    state.conversation_history.append({
        "user": message,
        "bot": response,
        "intent": "package",
        "step": "extract_slots"
    })
    
    return {
        "response": response,
        "next_action": "build_query",
        "state": state,
        "completed": False
    }

async def _build_query_step(state: ConversationState, message: str) -> Dict[str, Any]:
    """Third step: Build final query/response based on collected information"""
    package_data = PackageData(**state.collected_data["package_data"])
    operation = state.collected_data.get("package_operation", "unknown")
    
    if operation == "send":
        response = _build_shipping_summary(package_data)
    elif operation == "track":
        response = _build_tracking_response(package_data)
    else:
        response = "I need more information to help you with your package request."
    
    # Mark as completed
    state.conversation_history.append({
        "user": message,
        "bot": response,
        "intent": "package",
        "step": "build_query"
    })
    
    return {
        "response": response,
        "next_action": "completed",
        "state": state,
        "completed": True
    }

def _extract_shipping_info(message: str) -> Dict[str, str]:
    """Extract shipping information from message"""
    info = {}
    
    # Extract weight
    weight_match = re.search(r'(\d+(?:\.\d+)?)\s*(kg|lb|pound|kilogram)', message.lower())
    if weight_match:
        info["weight"] = f"{weight_match.group(1)} {weight_match.group(2)}"
    
    # Extract destination (simple city/country detection)
    # This is a simplified version - in production, you'd use more sophisticated NER
    cities = ["new york", "london", "tokyo", "paris", "sydney", "toronto", "bangkok"]
    for city in cities:
        if city in message.lower():
            info["destination"] = city.title()
            break
    
    return info

def _determine_next_package_question(package_data: PackageData, operation: str) -> str:
    """Determine what information is still needed"""
    if operation == "send":
        if not package_data.destination:
            return "Where would you like to send the package? (destination city/country)"
        elif not package_data.weight:
            return "What's the approximate weight of the package?"
        elif not package_data.recipient_name:
            return "What's the recipient's name?"
        else:
            return "Great! I have all the information needed. Let me prepare your shipping details."
    
    elif operation == "track":
        if not package_data.package_id:
            return "Please provide your tracking number (usually 8+ characters with letters and numbers)."
        else:
            return "Let me look up your package status."
    
    return "How else can I help you with your package?"

def _build_shipping_summary(package_data: PackageData) -> str:
    """Build shipping summary response"""
    summary = "Here's your shipping summary:\n\n"
    
    if package_data.destination:
        summary += f"📍 Destination: {package_data.destination}\n"
    if package_data.weight:
        summary += f"⚖️ Weight: {package_data.weight}\n"
    if package_data.recipient_name:
        summary += f"👤 Recipient: {package_data.recipient_name}\n"
    
    summary += "\n📦 Your package will be processed and you'll receive a tracking number within 24 hours."
    summary += "\n💰 Estimated cost and delivery time will be provided once we process your request."
    
    return summary

def _build_tracking_response(package_data: PackageData) -> str:
    """Build tracking response (simulated)"""
    if package_data.package_id:
        # Simulate tracking response
        return f"""📦 Package Status for {package_data.package_id}:

🚚 Status: In Transit
📍 Current Location: Distribution Center
📅 Expected Delivery: Tomorrow by 6:00 PM
🔄 Last Update: 2 hours ago

Your package is on its way and should arrive as scheduled!"""
    
    return "I need a valid tracking number to check your package status."
