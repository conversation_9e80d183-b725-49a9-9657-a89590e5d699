import re
from typing import <PERSON><PERSON>
from openai import OpenAI
from app.config import settings

class IntentClassifier:
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None
        
        # Rule-based patterns for fallback
        self.intent_patterns = {
            "greeting": [
                r"\b(hello|hi|hey|good morning|good afternoon|good evening)\b",
                r"\b(how are you|what's up)\b"
            ],
            "booking": [
                r"\b(book|appointment|schedule|reserve|reservation)\b",
                r"\b(available|availability|free time|slot)\b",
                r"\b(doctor|consultation|checkup|visit)\b"
            ],
            "info": [
                r"\b(information|info|tell me|what is|how to|help)\b",
                r"\b(hours|location|address|phone|contact)\b",
                r"\b(services|treatment|procedure)\b"
            ],
            "package": [
                r"\b(package|parcel|delivery|shipment|tracking)\b",
                r"\b(send|ship|mail|courier)\b",
                r"\b(weight|destination|recipient)\b"
            ]
        }
    
    async def classify_intent(self, message: str) -> Tuple[str, float]:
        """
        Classify user intent using OpenAI or fallback to rule-based classification
        """
        if self.client and settings.OPENAI_API_KEY:
            return await self._classify_with_openai(message)
        else:
            return self._classify_with_rules(message)
    
    async def _classify_with_openai(self, message: str) -> Tuple[str, float]:
        """Use OpenAI for intent classification"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": """You are an intent classifier for a receptionist chatbot. 
                        Classify the user message into one of these intents:
                        - booking: User wants to book an appointment or service
                        - greeting: User is greeting or starting conversation
                        - info: User wants information about services, hours, location, etc.
                        - package: User wants to send/track packages or deliveries
                        - unknown: Message doesn't fit any category
                        
                        Respond with only the intent name and confidence (0-1) in format: intent,confidence"""
                    },
                    {"role": "user", "content": message}
                ],
                temperature=0.1,
                max_tokens=20
            )
            
            result = response.choices[0].message.content.strip()
            parts = result.split(',')
            
            if len(parts) == 2:
                intent = parts[0].strip().lower()
                confidence = float(parts[1].strip())
                
                if intent in settings.SUPPORTED_INTENTS:
                    return intent, confidence
                    
        except Exception as e:
            print(f"OpenAI classification error: {e}")
        
        # Fallback to rule-based
        return self._classify_with_rules(message)
    
    def _classify_with_rules(self, message: str) -> Tuple[str, float]:
        """Rule-based intent classification as fallback"""
        message_lower = message.lower()
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message_lower):
                    return intent, 0.8
        
        return "unknown", 0.5
